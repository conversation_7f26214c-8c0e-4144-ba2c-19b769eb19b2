<!--
 * @Date         : 2023-06-16 16:10:14
 * @Description  : 团队业绩排行榜
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="team-rank-container relative overflow-hidden">
    <div class="top-98px left-10px absolute overflow-hidden h-220px">
      <van-swipe vertical
                 autoplay="20000"
                 :show-indicators="false">
        <van-swipe-item v-for="(ite, idx) in rankListHandle"
                        :key="idx">
          <div v-for="(item, index) of ite"
               :key="index"
               :class="{'top': checkTop(idx, index)}"
               class="rank-item">
            <div v-if="checkTop(idx, index)"
                 class="rank-icon-box">
              <Medal :rank="index + 1" />
            </div>
            <div v-else>NO.{{ (idx * 5) + index + 1 }}</div>
            <div>{{ sliceStr(item.group) }}</div>
            <div>{{ formatAmount(item.amount) }}</div>

            <Progress :progress="item.rate"
                      :is-top="checkTop(idx, index)" />
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { sliceStr, formatAmount } from '@/utils'
import { useTeam } from '@/store/team'
import Medal from '@/components/Medal.vue'
import Progress from '@/components/Progress.vue'

const teamStore = useTeam()

const rankListHandle = computed(() => {
  const target = teamStore.teamRankList.reduce((prev, curr, index) => {
    const i = Math.floor(index / 5)
    prev[i] = [...prev[i] || [], ...[curr]]
    return prev
  }, [])
  return target
})

function checkTop(idx, index) {
  return idx === 0 && (index === 0 || index === 1 || index === 2)
}
</script>

<style scoped lang='scss'>
.team-rank-container {
  background: url('@/assets/model/team_rank.png') no-repeat;
  background-size: 100% 100%;
  width: 728px;
  height: 339px;

  :deep(.van-swipe__track) {
    width: 709px;
  }

  .rank-item {
    display: flex;
    position: relative;
    align-items: center;
    margin-bottom: 1.6px;

    font-size: 20px;
    color: white;
    width: 100%;
    > div {
      text-align: center;
      position: absolute;
    }
    > div:nth-child(1) {
      left: 40px;
      width: 60px;
      display: flex;
      justify-content: center;
    }
    > div:nth-child(2) {
      left: 128px;
      width: 200px;
    }
    > div:nth-child(3) {
      width: 120px;
      left: 347px;
    }
    > div:nth-child(4) {
      left: 500px;
      width: 180px;
    }
  }

  .rank-item {
    background: url('@/assets/line_team_bg.png') no-repeat;
    background-size: 100% 100%;
    height: 35px;
    width: 693px;
  }
}

.top {
  color: #FFEE9F !important;
  :deep(div) {
    color: #FFEE9F !important;
  }
}

</style>
